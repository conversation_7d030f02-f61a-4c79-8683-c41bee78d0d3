import Toast, { POSITION, TYPE } from "vue-toastification"
import "vue-toastification/dist/index.css"

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(Toast, {
    // Cấu hình toast
    position: POSITION.TOP_RIGHT,
    timeout: 3000,
    closeOnClick: true,
    pauseOnFocusLoss: true,
    pauseOnHover: true,
    draggable: true,
    draggablePercent: 0.6,
    showCloseButtonOnHover: false,
    hideProgressBar: false,
    closeButton: "button",
    icon: true,
    rtl: false,
    transition: "Vue-Toastification__bounce",
    maxToasts: 5,
    newestOnTop: true,

    // Custom styling
    toastDefaults: {
      [TYPE.SUCCESS]: {
        timeout: 3000,
        hideProgressBar: false,
      },
      [TYPE.ERROR]: {
        timeout: 5000,
        hideProgressBar: false,
      },
      [TYPE.INFO]: {
        timeout: 3000,
        hideProgressBar: false,
      },
      [TYPE.WARNING]: {
        timeout: 4000,
        hideProgressBar: false,
      }
    }
  })
})
