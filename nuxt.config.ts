// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  
  // Đăng ký các modules
  modules: [
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
  ],

  // C<PERSON><PERSON> hình CSS global
  css: [
    '~/assets/css/main.css',
  ],

  // Cấu hình Pinia
  pinia: {
    storesDirs: ['./stores/**'],
  },

  // Quản lý biến môi trường
  runtimeConfig: {
    public: {
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'https://api.example.com',
      apiKey: process.env.NUXT_PUBLIC_API_KEY || '',
    }
  }
})