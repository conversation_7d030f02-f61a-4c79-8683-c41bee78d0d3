<template>
  <BaseModal
    :is-open="isOpen"
    title="Quên mật khẩu"
    size="lg"
    body-class="w-[452px] mx-auto"
    @close="closeModal"
    @open="onModalOpen"
  >
    <form @submit="onFormSubmit" class="space-y-6">
      <!-- Description -->
      <div class="text-center mb-6">
        <p class="text-gray-600 text-sm leading-relaxed">
          Nhập email của bạn để nhận liên kết đặt lại mật khẩu
        </p>
      </div>

      <div class="space-y-6">
        <!-- Email Field -->
        <div class="flex flex-col gap-3">
          <label class="text-black text-base font-bold leading-[1.2]">
            Email
          </label>
          <div class="relative">
            <Field
              name="email"
              type="email"
              :class="[
                'w-full h-[54px] px-[14px] border rounded-lg text-base font-normal leading-[1.2] placeholder-[#919EAB] focus:outline-none',
                errors.email ? 'border-red-500 focus:border-red-500' : 'border-[#E9ECEE] focus:border-[#0066B3]'
              ]"
              placeholder="Nhập Email"
            />
          </div>
          <ErrorMessage name="email" class="text-xs text-red-600 min-h-[16px]" />
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          :disabled="isLoading"
          class="w-full bg-[#0D68B2] hover:bg-[#0056a3] disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold text-xl leading-[1.22] h-[52px] rounded-[14.613px] shadow-[0px_10.96px_36.532px_0px_rgba(0,0,0,0.15)] transition-colors flex items-center justify-center gap-3"
        >
          <span v-if="isLoading" class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></span>
          <span class="text-[#F6F6F6] text-xl font-bold leading-[1.22] text-center">
            {{ isLoading ? 'Đang gửi...' : 'Gửi liên kết' }}
          </span>
        </button>
      </div>

      <!-- Back to Login Link -->
      <div class="flex justify-center items-center text-base">
        <button type="button" @click="switchToLogin" class="text-[#4F63EE] hover:underline flex items-center gap-1">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Quay lại đăng nhập
        </button>
      </div>
    </form>

  </BaseModal>
</template>

<script setup lang="ts">
import { Field, ErrorMessage, useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import BaseModal from '../BaseModal.vue'
import { useAuthStore } from '~/stores/auth'

interface Props {
  isOpen: boolean
}

interface Emits {
  close: []
  switchToLogin: []
  resetSuccess: [email: string]
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// Reactive variables
const isLoading = ref(false)

// Toast composable
const { showSuccess, showError } = useToast()

// Zod schema
const zodSchema = z.object({
  email: z.string().min(1, 'Email là bắt buộc').email('Email không hợp lệ'),
})

type FormData = z.infer<typeof zodSchema>

// Form setup
const { handleSubmit, errors, resetForm } = useForm({
  validationSchema: toTypedSchema(zodSchema),
  initialValues: {
    email: ''
  }
})

// Notifications (using Vue Toastification)
const showToastNotification = (message: string) => {
  showSuccess(message)
}

const showErrorNotification = (message: string) => {
  showError(message)
}

// Modal handlers
const closeModal = () => {
  emit('close')
  resetForm()
}

const onModalOpen = () => {
  resetForm()
}

const switchToLogin = () => {
  emit('switchToLogin')
}

// Form submission
const onSubmit = async (values: FormData) => {
  isLoading.value = true

  try {
    const authStore = useAuthStore()
    const result = await authStore.forgotPassword(values.email)

    if (result.success) {
      showToastNotification(result.message)

      setTimeout(() => {
        emit('resetSuccess', values.email)
        emit('switchToLogin')
      }, 2000)
    }
  } catch (error: any) {
    showErrorNotification(error.message || 'Có lỗi xảy ra. Vui lòng thử lại.')
  } finally {
    isLoading.value = false
  }
}

const onFormSubmit = handleSubmit(onSubmit)
</script>
